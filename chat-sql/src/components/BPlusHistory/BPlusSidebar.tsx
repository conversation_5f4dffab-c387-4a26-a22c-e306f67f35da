/**
 * B+树页面侧边栏组件
 * 参考ER图页面和主页面的侧边栏实现
 */

'use client';

import React, { useState } from 'react';
import { Button, Tooltip, Modal, Switch } from 'antd';
import {
  HistoryOutlined,
  QuestionCircleOutlined,
  GithubOutlined,
  EyeOutlined,
  EyeInvisibleOutlined
} from '@ant-design/icons';
import styles from './BPlusSidebar.module.css';

interface BPlusSidebarProps {
  /** 是否显示历史记录区域 */
  showHistory: boolean;
  /** 历史记录区域显示状态变更回调 */
  onToggleHistory: (show: boolean) => void;
}

const BPlusSidebar: React.FC<BPlusSidebarProps> = ({
  showHistory,
  onToggleHistory
}) => {
  const [isHelpModalOpen, setIsHelpModalOpen] = useState(false);

  const handleToggleHistory = (checked: boolean) => {
    onToggleHistory(checked);
  };

  const handleHelpClick = () => {
    setIsHelpModalOpen(true);
  };

  const handleGithubClick = () => {
    window.open('https://github.com/ffy6511/chatSQL', '_blank');
  };

  return (
    <div className="global-sidebar-container">
      {/* 顶部区域：历史记录切换开关 */}
      <div className="global-sidebar-top-buttons">
        <Tooltip title={showHistory ? "隐藏历史记录" : "显示历史记录"} placement="right">
          <div className={styles.historyToggleContainer}>
            <Button
              type="text"
              icon={showHistory ? <EyeOutlined /> : <EyeInvisibleOutlined />}
              className="global-sidebar-action-button"
              onClick={() => handleToggleHistory(!showHistory)}
            />
            <Switch
              size="small"
              checked={showHistory}
              onChange={handleToggleHistory}
              style={{
                backgroundColor: showHistory ? 'var(--link-color)' : 'var(--button-hover)'
              }}
            />
          </div>
        </Tooltip>
      </div>

      {/* 中间区域：可扩展的功能按钮 */}
      <div className="global-sidebar-menu-container">
        <div className="global-sidebar-menu-items">
          <Tooltip title="历史记录" placement="right">
            <Button
              type="text"
              icon={<HistoryOutlined />}
              className={`global-sidebar-action-button${showHistory ? ' selected' : ''}`}
              onClick={() => handleToggleHistory(true)}
            />
          </Tooltip>
        </div>
      </div>
      
      {/* 底部区域：帮助和GitHub链接 */}
      <div className="global-sidebar-bottom-buttons">
        <Tooltip title="帮助" placement="right">
          <Button 
            type="text" 
            icon={<QuestionCircleOutlined />}
            className="global-sidebar-action-button"
            onClick={handleHelpClick}
          />
        </Tooltip>
        
        <Tooltip title="GitHub仓库" placement="right">
          <Button 
            type="text" 
            icon={<GithubOutlined />}
            className="global-sidebar-action-button"
            onClick={handleGithubClick}
          />
        </Tooltip>
      </div>

      {/* 帮助模态框 */}
      <Modal
        title="B+树可视化帮助"
        open={isHelpModalOpen}
        onCancel={() => setIsHelpModalOpen(false)}
        footer={null}
        width={600}
      >
        <div className={styles.helpModalContent}>
          <h3>B+树可视化学习工具</h3>
          <p>这是一个交互式的B+树可视化学习工具，帮助您理解B+树的数据结构和操作原理。</p>
          
          <h4>主要功能：</h4>
          <ul>
            <li><strong>可视化渲染</strong>：直观显示B+树的节点结构和连接关系</li>
            <li><strong>交互式操作</strong>：支持插入、删除、重置等基本操作</li>
            <li><strong>动画演示</strong>：可选择启用动画来观察操作过程</li>
            <li><strong>历史记录</strong>：记录每个操作步骤，支持回溯查看</li>
            <li><strong>智能助手</strong>：提供B+树相关问题的解答和指导</li>
          </ul>

          <h4>使用方法：</h4>
          <ol>
            <li>在右下角的操作面板中设置B+树的阶数</li>
            <li>使用插入功能添加键值到B+树中</li>
            <li>使用删除功能移除指定的键值</li>
            <li>在左侧历史面板中查看操作记录</li>
            <li>点击历史步骤可以回溯到对应状态</li>
            <li>使用智能助手获取学习指导</li>
          </ol>

          <h4>学习建议：</h4>
          <ul>
            <li>从小阶数（如3阶）开始练习，理解基本概念</li>
            <li>观察节点分裂和合并的条件和过程</li>
            <li>注意叶子节点之间的链表连接</li>
            <li>对比不同阶数下的树结构差异</li>
          </ul>

          <h4>技术特性：</h4>
          <ul>
            <li>支持正负整数和零的插入删除</li>
            <li>自动布局和美化显示</li>
            <li>响应式设计，适配不同屏幕尺寸</li>
            <li>本地存储，数据持久化保存</li>
          </ul>
        </div>
      </Modal>
    </div>
  );
};

export default BPlusSidebar;
