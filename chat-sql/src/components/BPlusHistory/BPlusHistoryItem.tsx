/**
 * B+树历史记录项组件
 * 参考HistoryItem.tsx的样式设计，实现悬浮效果、选中状态和操作按钮显示
 */

'use client';

import React, { useState } from 'react';
import {
  EditOutlined,
  DeleteOutlined,
  PlayArrowOutlined,
  AddOutlined,
  RemoveOutlined,
  RefreshOutlined,
  FiberManualRecordOutlined
} from '@mui/icons-material';
import { HistorySession, HistoryStep } from '@/types/bPlusHistory';
import styles from './BPlusHistoryItem.module.css';

interface BPlusHistorySessionItemProps {
  session: HistorySession;
  isActive: boolean;
  onSelect: (sessionId: string) => void;
  onRename: (sessionId: string, newName: string) => void;
  onDelete: (sessionId: string) => void;
}

interface BPlusHistoryStepItemProps {
  step: HistoryStep;
  stepIndex: number;
  isActive: boolean;
  isCurrent: boolean;
  onSelect: (stepIndex: number) => void;
}

// 格式化时间显示
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  return date.toLocaleString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// 获取操作图标和样式
const getOperationIcon = (operation: string) => {
  switch (operation) {
    case 'insert':
      return <AddOutlined fontSize="small" />;
    case 'delete':
      return <RemoveOutlined fontSize="small" />;
    case 'reset':
      return <RefreshOutlined fontSize="small" />;
    case 'initial':
      return <FiberManualRecordOutlined fontSize="small" />;
    default:
      return <FiberManualRecordOutlined fontSize="small" />;
  }
};

// 会话项组件
export const BPlusHistorySessionItem: React.FC<BPlusHistorySessionItemProps> = ({
  session,
  isActive,
  onSelect,
  onRename,
  onDelete
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(session.name);

  const handleRename = () => {
    if (editName.trim() && editName.trim() !== session.name) {
      onRename(session.id, editName.trim());
    }
    setIsEditing(false);
  };

  const handleCancelEdit = () => {
    setEditName(session.name);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleRename();
    } else if (e.key === 'Escape') {
      handleCancelEdit();
    }
  };

  return (
    <div className={`${styles.globalStylesContainer}`}>
      <div
        className={`${styles.historyItem} ${isActive ? styles.active : ''}`}
        onClick={() => !isEditing && onSelect(session.id)}
      >
        {isEditing ? (
          <div className={styles.editingContainer} onClick={e => e.stopPropagation()}>
            <input
              className={styles.editingInput}
              value={editName}
              onChange={(e) => setEditName(e.target.value)}
              onKeyDown={handleKeyDown}
              autoFocus
              maxLength={50}
              placeholder="输入会话名称"
              aria-label="会话名称"
            />
            <div className={styles.editingButtons}>
              <button
                type="button"
                className={`${styles.editingButton} ${styles.primary}`}
                onClick={handleRename}
              >
                确定
              </button>
              <button
                type="button"
                className={styles.editingButton}
                onClick={handleCancelEdit}
              >
                取消
              </button>
            </div>
          </div>
        ) : (
          <>
            <div className={styles.sessionItem}>
              <div className={styles.sessionTitle}>
                {session.name}
              </div>
              <div className={styles.sessionInfo}>
                <div className={styles.sessionMeta}>
                  <span>{formatTime(session.updatedAt)}</span>
                  <span>•</span>
                  <span>{session.steps.length} 步骤</span>
                </div>
                <div className={styles.sessionTags}>
                  <span className={styles.sessionTag}>
                    阶数 {session.order}
                  </span>
                  {session.isCompleted && (
                    <span className={styles.sessionTag}>
                      已完成
                    </span>
                  )}
                </div>
              </div>
            </div>

            <div className={styles.actionButtons} onClick={e => e.stopPropagation()}>
              <button
                type="button"
                className={styles.actionButton}
                onClick={() => setIsEditing(true)}
                title="重命名"
              >
                <EditOutlined fontSize="small" />
              </button>
              <button
                type="button"
                className={`${styles.actionButton} ${styles.danger}`}
                onClick={() => onDelete(session.id)}
                title="删除"
              >
                <DeleteOutlined fontSize="small" />
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

// 步骤项组件
export const BPlusHistoryStepItem: React.FC<BPlusHistoryStepItemProps> = ({
  step,
  stepIndex,
  isActive,
  isCurrent,
  onSelect
}) => {
  return (
    <div className={`${styles.globalStylesContainer}`}>
      <div
        className={`${styles.historyItem} ${isActive ? styles.active : ''}`}
        onClick={() => onSelect(stepIndex)}
      >
        <div className={styles.stepItem}>
          <div className={`${styles.stepIcon} ${styles[step.operation]}`}>
            {getOperationIcon(step.operation)}
          </div>
          
          <div className={styles.stepContent}>
            <div className={styles.stepDescription}>
              {step.description}
            </div>
            <div className={styles.stepTime}>
              {formatTime(step.timestamp)}
            </div>
          </div>
          
          {isCurrent && (
            <div className={styles.currentStepIndicator}>
              <PlayArrowOutlined fontSize="small" />
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
